<?php

namespace App\Http\Resources;

use App\Services\Storage\S3StorageService;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property string $id
 * @property string $name
 * @property string $path
 * @property int $size
 * @property string $mime_type
 * @property string $type
 * @property string $created_at
 * @property bool $is_private
 */
class FileResource extends JsonResource
{
    public function toArray($request): array
    {
        /** @var S3StorageService $storageService */
        $storageService = app(S3StorageService::class);

        return [
            'id' => $this->id,
            'name' => $this->name,
            'path' => $storageService->getUrl($this->path, $this->is_private),
            'size' => $this->size,
            'mime_type' => $this->mime_type,
            'type' => $this->type,
            'created_at' => $this->created_at,
        ];
    }
}
