<?php

namespace App\Http\Controllers\Api\Internal\Other;

use App\Contracts\Services\Internal\SuggestsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\Dadata\AddressRequest;
use App\Http\Requests\Api\Internal\Dadata\BankRequest;
use App\Http\Requests\Api\Internal\Dadata\FindPartyRequest;
use Illuminate\Http\JsonResponse;

class SuggestController extends Controller
{
    public function __construct(
        private readonly SuggestsServiceContract $service
    ) {
    }

    /**
     * @response array{suggestions: array<array{value: string, unrestricted_value: string, data: array}>}
     */
    public function findParty(FindPartyRequest $request): JsonResponse
    {
        $result = $this->service->party($request->toDTO());

        return response()->json($result, 200);
    }

    /**
     * @response array{suggestions: array<array{value: string, unrestricted_value: string, data: array{bic: string, name: array{payment: string}}}>}
     */
    public function bank(BankRequest $request): JsonResponse
    {
        $result = $this->service->bank($request->toDTO());

        return response()->json($result, 200);
    }

    /**
     * @response array{suggestions: array<array{value: string, unrestricted_value: string, data: array{postal_code: string, country: string, region: string, city: string}}>}
     */
    public function address(AddressRequest $request): JsonResponse
    {
        $result = $this->service->address($request->toDTO());

        return response()->json($result, 200);
    }
}
